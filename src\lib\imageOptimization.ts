/**
 * Image optimization utilities
 */

/**
 * Generate responsive image sizes based on the viewport
 * @param baseSize Base size of the image in pixels
 * @returns A sizes string for the Next.js Image component
 */
export function generateResponsiveSizes(baseSize: number): string {
  return `(max-width: 640px) ${baseSize}px, (max-width: 768px) ${baseSize * 1.25}px, (max-width: 1024px) ${baseSize * 1.5}px, ${baseSize * 2}px`;
}

/**
 * Determine if an image should be loaded with priority
 * @param index Index of the image in a list
 * @param isAboveFold Whether the image is above the fold
 * @returns Boolean indicating if the image should be loaded with priority
 */
export function shouldPrioritizeImage(index: number, isAboveFold: boolean = false): boolean {
  // Prioritize first 2 images or any above-fold images
  return index < 2 || isAboveFold;
}

/**
 * Get optimal image quality based on importance
 * @param isPriority Whether the image is a priority image
 * @returns Quality value (1-100)
 */
export function getImageQuality(isPriority: boolean): number {
  return isPriority ? 85 : 75;
}

/**
 * Generate blur data URL for image placeholders
 * This is a simple implementation - for production, consider using a proper
 * solution that generates actual blur data URLs
 */
export function getBlurDataUrl(color: string = 'F8F7F4'): string {
  return `data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3Crect width='1' height='1' fill='%23${color}'/%3E%3C/svg%3E`;
}
