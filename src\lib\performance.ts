/**
 * Performance optimization utilities for the website
 */

/**
 * Preload critical resources
 * This function can be called in useEffect to preload resources that will be needed soon
 */
export function preloadResources() {
  if (typeof window === 'undefined') return;

  // Preload critical images
  const imagesToPreload = [
    '/images/spa-1.jpg',
    '/images/spa-2.jpg',
  ];

  imagesToPreload.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });
}

/**
 * Defer non-critical resources
 * This helps improve initial page load by deferring non-critical resources
 */
export function deferNonCriticalResources() {
  if (typeof window === 'undefined') return;
  
  // Add event listener for when the page is fully loaded
  window.addEventListener('load', () => {
    // Wait a bit after page load to start loading non-critical resources
    setTimeout(() => {
      // Load non-critical scripts or styles here if needed
      console.log('Loading deferred resources');
    }, 1000);
  });
}

// Add type declaration for deviceMemory
declare global {
  interface Navigator {
    deviceMemory?: number;
  }
}

/**
 * Optimize animations for performance
 * This function can be used to reduce animation complexity on low-end devices
 */
export function optimizeAnimations() {
  if (typeof window === 'undefined') return;
  
  // Check if the device is likely to be low-end
  const isLowEndDevice = () => {
    // Simple heuristic - can be improved
    return (
      navigator.hardwareConcurrency <= 4 || 
      (typeof navigator.deviceMemory !== 'undefined' && navigator.deviceMemory <= 4) || 
      window.matchMedia('(prefers-reduced-motion: reduce)').matches
    );
  };
  
  // Return settings based on device capability
  return {
    reducedMotion: isLowEndDevice(),
    // Other performance settings can be added here
  };
}
